// NOTE: Make sure to install the 'openai' package: npm install openai
import { NextRequest, NextResponse } from 'next/server';
import { readAgentState, writeAgentState } from '../../../lib/agentState';
import { streamText, generateText, generateObject } from 'ai';
import { createOpenRouter } from '@openrouter/ai-sdk-provider';
import FirecrawlApp from '@mendable/firecrawl-js';
import OpenAI from 'openai';
import { ollama } from 'ollama-ai-provider';
import { sendProgressUpdate } from './progress/route';
import { z } from 'zod';
import { createOpenAI } from '@ai-sdk/openai';

const openaiSdk = createOpenAI({
  apiKey: "********************************************************************************************************************************************************************"
});

const openrouter = createOpenRouter({
  apiKey: '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',
});
const firecrawl = new FirecrawlApp({ apiKey: 'not-needed', apiUrl: 'http://localhost:3002' });
const openai = new OpenAI({ apiKey: "********************************************************************************************************************************************************************" });

// Add a global in-memory progress log
let globalProgressLog: any[] = [];

function appendProgressLog(entry: any) {
  globalProgressLog.push(entry);
  // Also persist to state file if possible
  try {
    const state = readAgentState();
    state.progressLog = globalProgressLog;
    writeAgentState(state);
  } catch {}
}

// Patch sendProgressUpdate to also append to the log
function sendProgress(entry: any) {
  appendProgressLog(entry);
  sendProgressUpdate(entry);
}

export async function GET() {
  console.log('[API] GET /api/agent - Fetching agent state');
  const state = readAgentState();
  // Always include the progress log
  state.progressLog = globalProgressLog;
  console.log('[API] GET /api/agent - State retrieved:', { stage: state.stage, hasChat: !!state.chat?.length });
  return NextResponse.json(state);
}

export async function POST(req: NextRequest) {
  console.log('[API] POST /api/agent - Starting research process');

  try {
    const { startingPoint, extraRequest } = await req.json();
    console.log('[API] POST /api/agent - Request data:', { startingPoint, extraRequest });

    // Clear the global progress log for this new run
    globalProgressLog = [];
    appendProgressLog({ stage: 'Init', detail: 'Research process started', startingPoint, extraRequest });

    let state = readAgentState();
    let chatHistory: any[] = [];
    let researchContext = '';
    let searchList: string[] = [];
    let usedLinks: string[] = [];
    let scrapedResults: any[] = [];
    let scrapeErrors: string[] = [];
    let openaiLinksText = '';
    let currentStage = '';

    // 1. Use OpenAI to get relevant links for the query (if no searchList exists)
    if (startingPoint) {
      currentStage = 'OpenAI Search';
      sendProgress({ stage: currentStage, detail: `Requesting links for: ${startingPoint}` });
      const openaiResponse = await openai.responses.create({
        model: 'gpt-4.1',
        input: [
          {
            role: 'user',
            content: [
              {
                type: 'input_text',
                text: `give me the links for resources related to the query: ${startingPoint}.
just give me the links which are the most relevant use multiple searches to gather a vast context around the query plus arrange the links and give me the most relevant ones at top 3 and below them other related ones that can be used for more context. always provide links.`
              }
            ]
          },
          {
            id: 'ws_68452295a22081a38120ae1aa61dee41019c006be99371d6',
            type: 'web_search_call',
            status: 'completed'
          }
        ],
        text: { format: { type: 'text' } },
        reasoning: {},
        tools: [
          {
            type: 'web_search_preview',
            user_location: { type: 'approximate' },
            search_context_size: 'high'
          }
        ],
        temperature: 1,
        max_output_tokens: 2048,
        top_p: 1,
        store: true
      });
      const outputText = openaiResponse.output_text || '';
      openaiLinksText = outputText;
      // Use regex to extract all links
      searchList = Array.from(outputText.matchAll(/https?:\/\/[\w\-\.\/?#&=:%]+/g) as Iterable<RegExpMatchArray>).map(m => m[0]);
      sendProgress({ stage: currentStage, detail: 'OpenAI response received', outputText, searchList });
      chatHistory.push({ stage: currentStage, actions: [{ type: 'openai_search', prompt: startingPoint, response: outputText, searchList }], timestamp: Date.now() });
    }

    // 2. Scrape the top 3 links using Firecrawl, fallback to more if any fail
    currentStage = 'Scraping';
    if (searchList.length > 0) {
      let toTry = searchList.slice(0, 6); // Try up to 6 links to get at least 3 good ones
      for (let i = 0; i < toTry.length && scrapedResults.length < 3; i++) {
        try {
          const result = await firecrawl.scrapeUrl(toTry[i], { formats: ['markdown', 'links'] });
          if (result && result.success) {
            scrapedResults.push({
              title: result.title,
              url: result.url,
              markdown: result.markdown,
              links: result.links
            });
            usedLinks.push(toTry[i]);
            sendProgress({ stage: currentStage, detail: `Successfully scraped: ${toTry[i]}`, title: result.title });
          } else {
            scrapeErrors.push(`Failed to scrape: ${toTry[i]}`);
            sendProgress({ stage: currentStage, detail: `Failed to scrape: ${toTry[i]}` });
          }
        } catch (err) {
          scrapeErrors.push(`Error scraping ${toTry[i]}: ${err instanceof Error ? err.message : String(err)}`);
          sendProgress({ stage: currentStage, detail: `Error scraping: ${toTry[i]}`, error: err instanceof Error ? err.message : String(err) });
        }
      }
      sendProgress({ stage: currentStage, detail: `Scraping completed - ${scrapedResults.length} successful, ${scrapeErrors.length} errors` });
      chatHistory.push({ stage: currentStage, actions: [{ type: 'scraping', usedLinks: [...usedLinks], scrapeErrors: [...scrapeErrors], scrapedResults: [...scrapedResults] }], timestamp: Date.now() });
    }

    // 3. Summarize the combined content
    currentStage = 'Summarizing';
    let summary = '';
    if (scrapedResults.length > 0) {
      const combinedMarkdown = scrapedResults.map(r => r.markdown).join('\n\n');
      const ollamaRes = await generateText({
        model: openrouter.chat('deepseek/deepseek-r1-0528:free'),
        prompt: `Summarize the following research context for the query: ${startingPoint}\n\n${combinedMarkdown}`
      });
      summary = ollamaRes.text;
      researchContext = summary;
      sendProgress({ stage: currentStage, detail: 'Summary generated', summary });
      chatHistory.push({ stage: currentStage, actions: [{ type: 'summarize', summary }], timestamp: Date.now() });
    } else {
      summary = 'No content could be scraped for summary.';
      researchContext = summary;
      sendProgress({ stage: currentStage, detail: 'No content available for summarization' });
      chatHistory.push({ stage: currentStage, actions: [{ type: 'summarize', summary }], timestamp: Date.now() });
    }

    // 4. Agent Conversation Loop
    currentStage = 'Agent Conversation';
    let agent1Text = '';
    let agent2Text = '';
    let conversationTurns = 0;
    let maxTurns = 6;
    let stopConversation = false;
    let lastAgent1Output = '';
    let lastAgent2Output = '';
    let lastResearchContext = researchContext;
    let lastSearchList = searchList;
    let agentConversationHistory: any[] = [];
    let agent1CanFinish = false;

    // Compose detailed system prompt for both agents
    const systemDescription = `You are part of an automated research system. The system's goal is to research a given query, gather resources, scrape and summarize content, and then have two agents (Agent 1: Thinking, Agent 2: Searching) converse to analyze and generate a structured research context.\n\nThe 'search list' is NOT a list of links, but a list of queries or topics that should be researched further.\n\nYour role is to converse with the other agent, analyze the research context, and, if needed, generate a new search list for further research. If a search list already exists, focus on refining the research context. Always output your response in a structured format. When you believe the research is complete, output a final research context (in Markdown, with headings) and a search list (as an array of queries/topics) and indicate completion.`;

    while (!stopConversation && conversationTurns < maxTurns) {
      // Agent 1 (generateObject, Thinking)
      const agent1Prompt = `${systemDescription}\n\nYou are Agent 1 (Thinking).\n\nCurrent research context (Markdown):\n${lastResearchContext}\n\nCurrent search list (queries/topics to research further):\n${lastSearchList.length > 0 ? lastSearchList.join('\n') : 'None'}\n\nYour task: Analyze the context, converse with Agent 2, and provide a structured object with the following fields:\n- conversation: your message to Agent 2\n- researchContext: (optional) the research context in Markdown, with headings\n- searchList: (optional) an array of queries/topics to research further.\nYou can only submit the final research context and search list after at least one conversation with Agent 2. If you believe the research is complete, indicate so in your output.`;
      sendProgress({ stage: currentStage, detail: 'Agent 1 turn', prompt: agent1Prompt });
      const agent1Schema = z.object({
        conversation: z.string(),
        researchContext: z.string().nullable(),
        searchList: z.array(z.string()).nullable(),
      });
      const agent1Res = await generateObject({
        model: openaiSdk('o4-mini'),
        prompt: agent1Prompt,
        schema: agent1Schema,
        temperature: 0.8,
      });
      const agent1Obj = agent1Res.object || { conversation: '', researchContext: '', searchList: [] };
      agent1Text = agent1Obj.conversation || '';
      if (agent1Obj.researchContext) lastResearchContext = agent1Obj.researchContext;
      let agent1SearchList = Array.isArray(agent1Obj.searchList) ? agent1Obj.searchList : null;
      if (agent1SearchList && agent1SearchList.length > 0) lastSearchList = agent1SearchList;
      searchList = lastSearchList;
      agentConversationHistory.push({
        agent: 'agent1',
        summary: agent1Text.substring(0, 100),
        details: agent1Obj,
        prompt: agent1Prompt,
        timestamp: Date.now(),
      });
      sendProgress({ stage: currentStage, detail: 'Agent 1 response', response: agent1Obj });

      // Agent 1 can only finish after at least one conversation with Agent 2
      agent1CanFinish = conversationTurns > 0;
      if (agent1CanFinish && /research is complete|final research context/i.test(agent1Obj.researchContext || '')) {
        stopConversation = true;
        researchContext = agent1Obj.researchContext || lastResearchContext;
        searchList = agent1Obj.searchList || lastSearchList;
        break;
      }

      // Agent 2 (OpenAI, Searching)
      let agent2Text = '';
      let agent2SearchContext = '';
      let agent2SearchList = agent1SearchList && agent1SearchList.length > 0 ? agent1SearchList : null;
      let agent2Prompt = `${systemDescription}\n\nYou are Agent 2 (Searching).\n\nHere is what Agent 1 said:\n${agent1Text}\n\nCurrent research context (Markdown):\n${lastResearchContext}\n\nCurrent search list (queries/topics to research further):\n${lastSearchList.length > 0 ? lastSearchList.join('\n') : 'None'}\n\nYour task: Analyze Agent 1's output, converse with Agent 1, and provide deeper insights or next steps.`;
      if (agent2SearchList && agent2SearchList.length > 0) {
        agent2Prompt += `\n\nAdditionally, for each query in the search list, perform a web search and summarize the findings. Append your findings to the research context in Markdown format. The search list is: ${agent2SearchList.join(', ')}`;
      }
      agent2Prompt += `\nIf the search list is empty, propose a new search list as an array of queries/topics. Always output a structured object with two fields: researchContext (Markdown, with headings) and searchList (array of queries/topics). If you believe the research is complete, indicate so in your output.`;
      sendProgress({ stage: currentStage, detail: 'Agent 2 turn', prompt: agent2Prompt });
      const agent2Res = await openai.responses.create({
        model: 'gpt-4.1',
        input: [
            {
                role: 'system',
                content: systemDescription
            },
          {
            role: 'user',
            content: [
              {
                type: 'input_text',
                text: agent2Prompt
              }
            ]
          },
          {
            id: 'ws_68452295a22081a38120ae1aa61dee41019c006be99371d6',
            type: 'web_search_call',
            status: 'completed'
          }
        ],
        text: { format: { type: 'text' } },
        reasoning: {},
        tools: [
          {
            type: 'web_search_preview',
            user_location: { type: 'approximate' },
            search_context_size: 'high'
          }
        ],
        temperature: 1,
        max_output_tokens: 2048,
        top_p: 1,
        store: true
      });
      agent2Text = agent2Res.output_text || '';
      // Optionally, parse out new research context or search list if agent2 provides them in a structured way
      agentConversationHistory.push({
        agent: 'agent2',
        summary: agent2Text.substring(0, 100),
        details: agent2Text,
        prompt: agent2Prompt,
        timestamp: Date.now(),
      });
      sendProgress({ stage: currentStage, detail: 'Agent 2 response', response: agent2Text });

      // Check for completion
      if (/research is complete|final research context/i.test(agent2Text)) {
        stopConversation = true;
        researchContext = agent2Text;
        break;
      }

      conversationTurns++;
    }
    chatHistory.push({ stage: currentStage, actions: agentConversationHistory, timestamp: Date.now() });

    // Update state after conversation
    state = {
      ...state,
      stage: currentStage,
      progressLog: globalProgressLog,
      usedLinks,
      searchList,
      researchContext,
      chatHistory,
      extraRequest,
      searchResults: scrapedResults,
      scrapeErrors,
    };
    writeAgentState(state);
    sendProgress({ stage: 'Complete', detail: 'All stages complete', finalState: state });
    return NextResponse.json(state);

  } catch (error) {
    console.error('[API] POST /api/agent - Error occurred:', error);
    console.error('[API] Error stack:', error instanceof Error ? error.stack : 'No stack trace');
    return NextResponse.json(
      { error: 'Internal server error', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}