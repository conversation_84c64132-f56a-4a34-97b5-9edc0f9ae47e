'use client';

import { useState, useEffect, useRef } from 'react';

export default function Home() {
  const [agentState, setAgentState] = useState<any>(null);
  const [startingPoint, setStartingPoint] = useState('');
  const [extraRequest, setExtraRequest] = useState('');
  const [loading, setLoading] = useState(false);
  const [progressLog, setProgressLog] = useState<any[]>([]);
  const [showLog, setShowLog] = useState(false);
  const logEndRef = useRef<HTMLDivElement>(null);
  const [selectedLog, setSelectedLog] = useState<any | null>(null);

  // Fetch agent state from API
  useEffect(() => {
    console.log('[Frontend] Component mounted, fetching initial agent state');
    fetch('/api/agent')
      .then(res => {
        console.log('[Frontend] Initial state fetch response received:', res.status);
        return res.json();
      })
      .then(data => {
        console.log('[Frontend] Initial agent state loaded:', { stage: data.stage, hasChat: !!data.chat?.length });
        setAgentState(data);
      })
      .catch(error => {
        console.error('[Frontend] Error fetching initial agent state:', error);
      });
  }, []);

  // Use progressLog from agentState if available, else use live log
  useEffect(() => {
    if (agentState && Array.isArray(agentState.progressLog)) {
      setProgressLog(agentState.progressLog);
    }
  }, [agentState]);

  // Listen for real-time progress updates
  useEffect(() => {
    const es = new EventSource('/api/agent/progress');
    es.onmessage = (event) => {
      const data = JSON.parse(event.data);
      setProgressLog(logs => [...logs, data]);
    };
    return () => es.close();
  }, []);

  // Scroll log to bottom on new entry
  useEffect(() => {
    if (logEndRef.current) {
      logEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [progressLog]);

  // Log agent state changes
  useEffect(() => {
    if (agentState) {
      console.log('[Frontend] Agent state updated:', {
        stage: agentState.stage,
        chatMessages: agentState.chat?.length || 0,
        headings: agentState.headings?.length || 0,
        searchListItems: agentState.searchList?.length || 0,
        hasScratchpad: !!agentState.scratchpad,
        hasExtraRequest: !!agentState.extraRequest
      });
    }
  }, [agentState]);

  const handleStart = async () => {
    console.log('[Frontend] Starting research process with:', { startingPoint, extraRequest });
    setLoading(true);
    setProgressLog([]);

    try {
      console.log('[Frontend] Sending POST request to /api/agent');
      const res = await fetch('/api/agent', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ startingPoint, extraRequest })
      });

      console.log('[Frontend] API response received:', res.status);

      if (!res.ok) {
        throw new Error(`API request failed with status: ${res.status}`);
      }

      const data = await res.json();
      console.log('[Frontend] Research process completed, updating state:', {
        stage: data.stage,
        chatLength: data.chat?.length,
        hasError: !!data.error
      });

      if (data.error) {
        console.error('[Frontend] API returned error:', data.error);
      }

      setAgentState(data);
    } catch (error) {
      console.error('[Frontend] Error during research process:', error);
      // You might want to show an error message to the user here
    } finally {
      console.log('[Frontend] Research process finished, setting loading to false');
      setLoading(false);
    }
  };

  return (
    <div className="app-container">
      {/* Floating/collapsible progress log */}
      <div style={{ position: 'fixed', bottom: 24, right: 24, zIndex: 1000, display: 'flex', flexDirection: 'row', alignItems: 'flex-end' }}>
        {/* Log Panel */}
        <div>
          <button
            onClick={() => setShowLog(l => !l)}
            style={{ background: '#222', color: '#fff', borderRadius: 8, padding: '8px 16px', marginBottom: 8 }}
          >
            {showLog ? 'Hide' : 'Show'} Progress Log
          </button>
          {showLog && (
            <div style={{ width: 400, maxHeight: 400, overflowY: 'auto', background: '#181818', color: '#fff', borderRadius: 8, boxShadow: '0 2px 12px #0008', padding: 16, fontSize: 14 }}>
              <div style={{ fontWeight: 600, marginBottom: 8 }}>Live Progress</div>
              {progressLog.length === 0 && <div style={{ color: '#aaa' }}>No progress yet.</div>}
              {progressLog.map((log, i) => (
                <div
                  key={i}
                  style={{ marginBottom: 10, borderLeft: '3px solid #444', paddingLeft: 8, cursor: 'pointer', background: selectedLog === log ? '#222' : undefined }}
                  onClick={() => setSelectedLog(log)}
                  title="Click to view details"
                >
                  <div style={{ fontWeight: 500, color: '#6cf' }}>{log.stage}</div>
                  <div>{log.detail}</div>
                  {log.prompt && <div style={{ color: '#9f9', fontSize: 12, marginTop: 2 }}>Prompt: <span style={{ color: '#fff' }}>{log.prompt.slice(0, 200)}{log.prompt.length > 200 ? '...' : ''}</span></div>}
                  {log.snippet && <div style={{ color: '#ff9', fontSize: 12, marginTop: 2 }}>Snippet: <span style={{ color: '#fff' }}>{log.snippet}</span></div>}
                  {log.summary && <div style={{ color: '#9ff', fontSize: 12, marginTop: 2 }}>Summary: <span style={{ color: '#fff' }}>{log.summary.slice(0, 300)}{log.summary.length > 300 ? '...' : ''}</span></div>}
                  {log.response && <div style={{ color: '#fc9', fontSize: 12, marginTop: 2 }}>Response: <span style={{ color: '#fff' }}>{log.response.slice(0, 300)}{log.response.length > 300 ? '...' : ''}</span></div>}
                  {log.error && <div style={{ color: '#f66', fontSize: 12, marginTop: 2 }}>Error: {log.error}</div>}
                  {log.links && Array.isArray(log.links) && (
                    <div style={{ color: '#ccc', fontSize: 12, marginTop: 2 }}>Links: {log.links.slice(0, 5).join(', ')}{log.links.length > 5 ? '...' : ''}</div>
                  )}
                </div>
              ))}
              <div ref={logEndRef} />
            </div>
          )}
        </div>
        {/* Sidebox for selected log */}
        {showLog && selectedLog && (
          <div style={{ width: 420, maxHeight: 400, overflowY: 'auto', background: '#222', color: '#fff', borderRadius: 8, boxShadow: '0 2px 12px #0008', padding: 20, marginLeft: 16, position: 'relative', fontSize: 15, display: 'flex', flexDirection: 'column' }}>
            <button
              onClick={() => setSelectedLog(null)}
              style={{ position: 'absolute', top: 10, right: 10, background: '#333', color: '#fff', border: 'none', borderRadius: 4, padding: '2px 8px', cursor: 'pointer', fontSize: 16 }}
              title="Close"
            >×</button>
            <div style={{ fontWeight: 600, fontSize: 18, marginBottom: 8 }}>{selectedLog.stage}</div>
            <div style={{ marginBottom: 8, color: '#6cf' }}>{selectedLog.detail}</div>
            {(
              Object.entries(selectedLog).map(([key, value]) => (
                key !== 'stage' && key !== 'detail' && value && (
                  <div key={key} style={{ marginBottom: 10 }}>
                    <div style={{ fontWeight: 500, color: '#aaa', marginBottom: 2 }}>{key}:</div>
                    <pre style={{ background: '#181818', color: '#fff', borderRadius: 4, padding: 8, fontSize: 13, whiteSpace: 'pre-wrap', wordBreak: 'break-word', maxHeight: 180, overflowY: 'auto' }}>{typeof value === 'string' ? value : JSON.stringify(value, null, 2)}</pre>
                  </div>
                )
              )) as React.ReactNode[]
            )}
          </div>
        )}
      </div>
      {/* Sidebar for headings */}
      <aside className="sidebar">
        <h2>Sections</h2>
        <ul>
          {agentState?.headings?.map((h: any, i: number) => (
            <li key={i}><a href={`#${h.id}`}>{h.title}</a></li>
          ))}
        </ul>
      </aside>
      <main className="main-content">
        <h1>Research Agent Dashboard</h1>
        <div className="input-section">
          <input
            type="text"
            placeholder="Enter starting point..."
            value={startingPoint}
            onChange={e => {
              console.log('[Frontend] Starting point changed:', e.target.value);
              setStartingPoint(e.target.value);
            }}
            className="input-field"
          />
          <input
            type="text"
            placeholder="Extra request (optional)"
            value={extraRequest}
            onChange={e => {
              console.log('[Frontend] Extra request changed:', e.target.value);
              setExtraRequest(e.target.value);
            }}
            className="input-field"
          />
          <button
            onClick={() => {
              console.log('[Frontend] Start/Update button clicked');
              handleStart();
            }}
            disabled={loading}
          >
            {loading ? 'Processing...' : 'Start/Update'}
          </button>
        </div>
        <section className="section">
          <h2>Current Stage</h2>
          <div className="content-box">
            {agentState?.stage || 'Not started'}
          </div>
        </section>
        <section className="section">
          <h2>Agent State</h2>
          <pre className="code-block">
            {JSON.stringify(agentState, null, 2)}
          </pre>
        </section>
        <section className="section">
          <h2>Chat History</h2>
          <div className="content-box chat-history">
            {agentState?.chat?.map((msg: any, i: number) => (
              <div key={i} className="chat-message">
                <span className="chat-role">{msg.role}:</span> {msg.content}
              </div>
            )) || 'No chat yet.'}
          </div>
        </section>
        <section className="section">
          <h2>Research Context (Scratchpad)</h2>
          <div className="content-box">
            {agentState?.scratchpad || 'No context yet.'}
          </div>
        </section>
        <section className="section">
          <h2>Search List</h2>
          <ul className="search-list">
            {agentState?.searchList?.map((item: string, i: number) => (
              <li key={i}>{item}</li>
            )) || <li>No search items yet.</li>}
          </ul>
        </section>
      </main>
    </div>
  );
}