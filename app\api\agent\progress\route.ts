import { NextRequest } from 'next/server';

// In-memory list of connected clients (responses)
const clients: any[] = [];

export async function GET(req: NextRequest) {
  const { readable, writable } = new TransformStream();
  const writer = writable.getWriter();

  // Write initial headers for SSE
  writer.write(
    new TextEncoder().encode('retry: 10000\n\n')
  );

  // Add this writer to the list of clients
  clients.push(writer);

  // Remove client on close
  req.signal.addEventListener('abort', () => {
    const idx = clients.indexOf(writer);
    if (idx !== -1) clients.splice(idx, 1);
    writer.close();
  });

  return new Response(readable, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      Connection: 'keep-alive',
    },
  });
}

// Helper to send progress to all clients
export function sendProgressUpdate(data: any) {
  const msg = `data: ${JSON.stringify(data)}\n\n`;
  for (const writer of clients) {
    writer.write(new TextEncoder().encode(msg));
  }
} 